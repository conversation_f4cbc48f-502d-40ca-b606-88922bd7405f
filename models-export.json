[{"id": "babbage-002", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "babbage-002", "parent": null, "connection_type": "external", "name": "babbage-002", "openai": {"id": "babbage-002", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "babbage-002", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "hidden": false, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615351, "created_at": 1748615347}, {"id": "chatgpt-4o-latest", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "chatgpt-4o-latest", "parent": null, "connection_type": "external", "name": "chatgpt-4o-latest", "openai": {"id": "chatgpt-4o-latest", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "chatgpt-4o-latest", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615352, "created_at": 1748615352}, {"id": "claude-3-7-sonnet-20250219", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "claude-3-7-sonnet-20250219", "parent": null, "connection_type": "external", "name": "Claude 3.7 Sonnet", "openai": {"id": "claude-3-7-sonnet-20250219", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "claude-3-7-sonnet-20250219", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/claude-color.svg", "description": "Anthropic 的3.7旗舰模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "tags": [{"name": "<PERSON>"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748695875, "created_at": 1748615357}, {"id": "claude-3-7-sonnet-20250219-thinking", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "claude-3-7-sonnet-20250219-thinking", "parent": null, "connection_type": "external", "name": "Claude 3.7 Sonnet Thinking", "openai": {"id": "claude-3-7-sonnet-20250219-thinking", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "claude-3-7-sonnet-20250219-thinking", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": "Authropic 的3.7旗舰推理模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": []}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748695959, "created_at": 1748695959}, {"id": "dall-e-3", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "dall-e-3", "parent": null, "connection_type": "external", "name": "dall-e-3", "openai": {"id": "dall-e-3", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "dall-e-3", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "dall-e"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615360, "created_at": 1748615360}, {"id": "davinci-002", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "davinci-002", "parent": null, "connection_type": "external", "name": "davinci-002", "openai": {"id": "davinci-002", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "davinci-002", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615362, "created_at": 1748615362}, {"id": "deepseek-ai/DeepSeek-R1", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "deepseek-ai/DeepSeek-R1", "parent": null, "connection_type": "external", "name": "DeepSeek R1", "openai": {"id": "deepseek-ai/DeepSeek-R1", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "deepseek-ai/DeepSeek-R1", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/deepseek-color.svg", "description": "DeepSeek 的最强推理模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": [{"name": "DS"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748661616, "created_at": 1748661616}, {"id": "deepseek-ai/DeepSeek-V3", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "deepseek-ai/DeepSeek-V3", "parent": null, "connection_type": "external", "name": "DeepSeek V3", "openai": {"id": "deepseek-ai/DeepSeek-V3", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "deepseek-ai/DeepSeek-V3", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/deepseek-color.svg", "description": "DeepSeek 的非推理模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": [{"name": "DS"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748661653, "created_at": 1748661653}, {"id": "dify", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "dify", "parent": null, "connection_type": "external", "name": "dify", "openai": {"id": "dify", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "dify", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615362, "created_at": 1748615362}, {"id": "gpt-3.5-turbo", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo", "parent": null, "connection_type": "external", "name": "gpt-3.5-turbo", "openai": {"id": "gpt-3.5-turbo", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615363, "created_at": 1748615363}, {"id": "gpt-3.5-turbo-0125", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-0125", "parent": null, "connection_type": "external", "name": "gpt-3.5-turbo-0125", "openai": {"id": "gpt-3.5-turbo-0125", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-0125", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615364, "created_at": 1748615364}, {"id": "gpt-3.5-turbo-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-0613", "parent": null, "connection_type": "external", "name": "gpt-3.5-turbo-0613", "openai": {"id": "gpt-3.5-turbo-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-0613", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615365, "created_at": 1748615365}, {"id": "gpt-3.5-turbo-1106", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-1106", "parent": null, "connection_type": "external", "name": "gpt-3.5-turbo-1106", "openai": {"id": "gpt-3.5-turbo-1106", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-1106", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615365, "created_at": 1748615365}, {"id": "gpt-3.5-turbo-16k", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-16k", "parent": null, "connection_type": "external", "name": "gpt-3.5-turbo-16k", "openai": {"id": "gpt-3.5-turbo-16k", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-16k", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615365, "created_at": 1748615365}, {"id": "gpt-3.5-turbo-16k-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-16k-0613", "parent": null, "connection_type": "external", "name": "gpt-3.5-turbo-16k-0613", "openai": {"id": "gpt-3.5-turbo-16k-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-16k-0613", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615367, "created_at": 1748615367}, {"id": "gpt-3.5-turbo-instruct", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-instruct", "parent": null, "connection_type": "external", "name": "gpt-3.5-turbo-instruct", "openai": {"id": "gpt-3.5-turbo-instruct", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-3.5-turbo-instruct", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615368, "created_at": 1748615368}, {"id": "gpt-35-turbo", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-35-turbo", "parent": null, "connection_type": "external", "name": "gpt-35-turbo", "openai": {"id": "gpt-35-turbo", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-35-turbo", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748661679, "created_at": 1748661679}, {"id": "gpt-4", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4", "parent": null, "connection_type": "external", "name": "gpt-4", "openai": {"id": "gpt-4", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615368, "created_at": 1748615368}, {"id": "gpt-4-0125-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-0125-preview", "parent": null, "connection_type": "external", "name": "gpt-4-0125-preview", "openai": {"id": "gpt-4-0125-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-0125-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615369, "created_at": 1748615369}, {"id": "gpt-4-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-0613", "parent": null, "connection_type": "external", "name": "gpt-4-0613", "openai": {"id": "gpt-4-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-0613", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615369, "created_at": 1748615369}, {"id": "gpt-4-1106-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-1106-preview", "parent": null, "connection_type": "external", "name": "gpt-4-1106-preview", "openai": {"id": "gpt-4-1106-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-1106-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615370, "created_at": 1748615370}, {"id": "gpt-4-32k", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-32k", "parent": null, "connection_type": "external", "name": "gpt-4-32k", "openai": {"id": "gpt-4-32k", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-32k", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615371, "created_at": 1748615371}, {"id": "gpt-4-32k-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-32k-0613", "parent": null, "connection_type": "external", "name": "gpt-4-32k-0613", "openai": {"id": "gpt-4-32k-0613", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-32k-0613", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615374, "created_at": 1748615374}, {"id": "gpt-4-turbo", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-turbo", "parent": null, "connection_type": "external", "name": "gpt-4-turbo", "openai": {"id": "gpt-4-turbo", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-turbo", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615373, "created_at": 1748615373}, {"id": "gpt-4-turbo-2024-04-09", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-turbo-2024-04-09", "parent": null, "connection_type": "external", "name": "gpt-4-turbo-2024-04-09", "openai": {"id": "gpt-4-turbo-2024-04-09", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-turbo-2024-04-09", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615375, "created_at": 1748615375}, {"id": "gpt-4-turbo-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-turbo-preview", "parent": null, "connection_type": "external", "name": "gpt-4-turbo-preview", "openai": {"id": "gpt-4-turbo-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-turbo-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615375, "created_at": 1748615375}, {"id": "gpt-4-vision-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-vision-preview", "parent": null, "connection_type": "external", "name": "gpt-4-vision-preview", "openai": {"id": "gpt-4-vision-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4-vision-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615376, "created_at": 1748615376}, {"id": "gpt-41", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-41", "parent": null, "connection_type": "external", "name": "GPT 4.1", "openai": {"id": "gpt-41", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-41", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": "OpenAI 的快速编码和分析模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "tags": [{"name": "OpenAI"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748661869, "created_at": 1748661689}, {"id": "gpt-41-mini", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-41-mini", "parent": null, "connection_type": "external", "name": "gpt-41-mini", "openai": {"id": "gpt-41-mini", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-41-mini", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748661691, "created_at": 1748661691}, {"id": "gpt-41-nano", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-41-nano", "parent": null, "connection_type": "external", "name": "gpt-41-nano", "openai": {"id": "gpt-41-nano", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-41-nano", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748661736, "created_at": 1748661736}, {"id": "gpt-45-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-45-preview", "parent": null, "connection_type": "external", "name": "gpt-45-preview", "openai": {"id": "gpt-45-preview", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-45-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748661737, "created_at": 1748661737}, {"id": "gpt-4o", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o", "parent": null, "connection_type": "external", "name": "GPT 4o", "openai": {"id": "gpt-4o", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": "OpenAI的旗舰多模态模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "tags": [{"name": "OpenAI"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748661548, "created_at": 1748615377}, {"id": "gpt-4o-2024-05-13", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-2024-05-13", "parent": null, "connection_type": "external", "name": "gpt-4o-2024-05-13", "openai": {"id": "gpt-4o-2024-05-13", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-2024-05-13", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615377, "created_at": 1748615377}, {"id": "gpt-4o-2024-08-06", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-2024-08-06", "parent": null, "connection_type": "external", "name": "gpt-4o-2024-08-06", "openai": {"id": "gpt-4o-2024-08-06", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-2024-08-06", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615379, "created_at": 1748615379}, {"id": "gpt-4o-2024-11-20", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-2024-11-20", "parent": null, "connection_type": "external", "name": "gpt-4o-2024-11-20", "openai": {"id": "gpt-4o-2024-11-20", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-2024-11-20", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615379, "created_at": 1748615379}, {"id": "gpt-4o-audio-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-audio-preview", "parent": null, "connection_type": "external", "name": "gpt-4o-audio-preview", "openai": {"id": "gpt-4o-audio-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-audio-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615380, "created_at": 1748615380}, {"id": "gpt-4o-audio-preview-2024-10-01", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-audio-preview-2024-10-01", "parent": null, "connection_type": "external", "name": "gpt-4o-audio-preview-2024-10-01", "openai": {"id": "gpt-4o-audio-preview-2024-10-01", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-audio-preview-2024-10-01", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615380, "created_at": 1748615380}, {"id": "gpt-4o-mini", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-mini", "parent": null, "connection_type": "external", "name": "gpt-4o-mini", "openai": {"id": "gpt-4o-mini", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-mini", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615381, "created_at": 1748615381}, {"id": "gpt-4o-mini-2024-07-18", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-mini-2024-07-18", "parent": null, "connection_type": "external", "name": "gpt-4o-mini-2024-07-18", "openai": {"id": "gpt-4o-mini-2024-07-18", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-mini-2024-07-18", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615384, "created_at": 1748615384}, {"id": "gpt-4o-realtime-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-realtime-preview", "parent": null, "connection_type": "external", "name": "gpt-4o-realtime-preview", "openai": {"id": "gpt-4o-realtime-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-realtime-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615387, "created_at": 1748615387}, {"id": "gpt-4o-realtime-preview-2024-10-01", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-realtime-preview-2024-10-01", "parent": null, "connection_type": "external", "name": "gpt-4o-realtime-preview-2024-10-01", "openai": {"id": "gpt-4o-realtime-preview-2024-10-01", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "gpt-4o-realtime-preview-2024-10-01", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615387, "created_at": 1748615387}, {"id": "grok-3-latest", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "grok-3-latest", "parent": null, "connection_type": "external", "name": "grok-3-latest", "openai": {"id": "grok-3-latest", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "grok-3-latest", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/grok.svg", "description": null, "capabilities": null, "tags": [{"name": "xai"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615393, "created_at": 1748615393}, {"id": "grok-3-mini-beta", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "grok-3-mini-beta", "parent": null, "connection_type": "external", "name": "grok-3-mini-beta", "openai": {"id": "grok-3-mini-beta", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "grok-3-mini-beta", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/grok.svg", "description": null, "capabilities": null, "tags": [{"name": "xai"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615392, "created_at": 1748615392}, {"id": "mistral-medium-latest", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "mistral-medium-latest", "parent": null, "connection_type": "external", "name": "Mistral Medium", "openai": {"id": "mistral-medium-latest", "object": "model", "created": 1626777600, "owned_by": "mistral", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "mistral-medium-latest", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/mistral-color.svg", "description": "欧洲公司 Mistral 的最新模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": [{"name": "<PERSON><PERSON><PERSON>"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748662901, "created_at": 1748662901}, {"id": "o1", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1", "parent": null, "connection_type": "external", "name": "o1", "openai": {"id": "o1", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748661910, "created_at": 1748661910}, {"id": "o1-mini", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-mini", "parent": null, "connection_type": "external", "name": "o1-mini", "openai": {"id": "o1-mini", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-mini", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615390, "created_at": 1748615390}, {"id": "o1-mini-2024-09-12", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-mini-2024-09-12", "parent": null, "connection_type": "external", "name": "o1-mini-2024-09-12", "openai": {"id": "o1-mini-2024-09-12", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-mini-2024-09-12", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615390, "created_at": 1748615390}, {"id": "o1-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-preview", "parent": null, "connection_type": "external", "name": "o1-preview", "openai": {"id": "o1-preview", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-preview", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615391, "created_at": 1748615391}, {"id": "o1-preview-2024-09-12", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-preview-2024-09-12", "parent": null, "connection_type": "external", "name": "o1-preview-2024-09-12", "openai": {"id": "o1-preview-2024-09-12", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o1-preview-2024-09-12", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615394, "created_at": 1748615394}, {"id": "o3-mini", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o3-mini", "parent": null, "connection_type": "external", "name": "o3-mini", "openai": {"id": "o3-mini", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o3-mini", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "oai"}, {"name": "OpenAI"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748661933, "created_at": 1748661933}, {"id": "o4-mini", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o4-mini", "parent": null, "connection_type": "external", "name": "o4-mini", "openai": {"id": "o4-mini", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "o4-mini", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": "OpenAI 的快速推理和高级分析模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": [{"name": "OpenAI"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748661964, "created_at": 1748661964}, {"id": "Pro/BAAI/bge-m3", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Pro/BAAI/bge-m3", "parent": null, "connection_type": "external", "name": "Pro/BAAI/bge-m3", "openai": {"id": "Pro/BAAI/bge-m3", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Pro/BAAI/bge-m3", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/baai.svg", "description": null, "capabilities": null, "hidden": true, "tags": [{"name": "BAAI"}]}, "access_control": {}, "price": null, "is_active": true, "updated_at": 1748620318, "created_at": 1748620313}, {"id": "Pro/BAAI/bge-reranker-v2-m3", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Pro/BAAI/bge-reranker-v2-m3", "parent": null, "connection_type": "external", "name": "Pro/BAAI/bge-reranker-v2-m3", "openai": {"id": "Pro/BAAI/bge-reranker-v2-m3", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Pro/BAAI/bge-reranker-v2-m3", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/baai.svg", "description": null, "capabilities": null, "hidden": true, "tags": [{"name": "BAAI"}]}, "access_control": {}, "price": null, "is_active": true, "updated_at": 1748620314, "created_at": 1748620314}, {"id": "Qwen/Qwen3-235B-A22B", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Qwen/Qwen3-235B-A22B", "parent": null, "connection_type": "external", "name": "Qwen3 235B", "openai": {"id": "Qwen/Qwen3-235B-A22B", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Qwen/Qwen3-235B-A22B", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/qwen-color.svg", "description": "Qwen3 最大参数模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": [{"name": "qwen"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748662113, "created_at": 1748662113}, {"id": "Qwen/QwQ-32B", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Qwen/QwQ-32B", "parent": null, "connection_type": "external", "name": "QwQ-32B", "openai": {"id": "Qwen/QwQ-32B", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Qwen/QwQ-32B", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/qwen-color.svg", "description": "阿里的推理模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": [{"name": "qwen"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": false, "updated_at": 1748662136, "created_at": 1748662128}, {"id": "text-ada-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-ada-001", "parent": null, "connection_type": "external", "name": "text-ada-001", "openai": {"id": "text-ada-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-ada-001", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615394, "created_at": 1748615394}, {"id": "text-babbage-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-babbage-001", "parent": null, "connection_type": "external", "name": "text-babbage-001", "openai": {"id": "text-babbage-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-babbage-001", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615395, "created_at": 1748615395}, {"id": "text-curie-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-curie-001", "parent": null, "connection_type": "external", "name": "text-curie-001", "openai": {"id": "text-curie-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-curie-001", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615396, "created_at": 1748615396}, {"id": "text-davinci-edit-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-davinci-edit-001", "parent": null, "connection_type": "external", "name": "text-davinci-edit-001", "openai": {"id": "text-davinci-edit-001", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-davinci-edit-001", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615397, "created_at": 1748615397}, {"id": "text-embedding-3-large", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-embedding-3-large", "parent": null, "connection_type": "external", "name": "text-embedding-3-large", "openai": {"id": "text-embedding-3-large", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-embedding-3-large", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "embedding"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615397, "created_at": 1748615397}, {"id": "text-embedding-3-small", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-embedding-3-small", "parent": null, "connection_type": "external", "name": "text-embedding-3-small", "openai": {"id": "text-embedding-3-small", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-embedding-3-small", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "embedding"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615398, "created_at": 1748615398}, {"id": "text-embedding-ada-002", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-embedding-ada-002", "parent": null, "connection_type": "external", "name": "text-embedding-ada-002", "openai": {"id": "text-embedding-ada-002", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-embedding-ada-002", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "embedding"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615399, "created_at": 1748615399}, {"id": "text-moderation-latest", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-moderation-latest", "parent": null, "connection_type": "external", "name": "text-moderation-latest", "openai": {"id": "text-moderation-latest", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-moderation-latest", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615399, "created_at": 1748615399}, {"id": "text-moderation-stable", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-moderation-stable", "parent": null, "connection_type": "external", "name": "text-moderation-stable", "openai": {"id": "text-moderation-stable", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "text-moderation-stable", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "/static/favicon.png", "description": null, "capabilities": null, "tags": []}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615401, "created_at": 1748615401}, {"id": "Tongyi-Zhiwen/QwenLong-L1-32B", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Tongyi-Zhiwen/QwenLong-L1-32B", "parent": null, "connection_type": "external", "name": "QwenLong L1", "openai": {"id": "Tongyi-Zhiwen/QwenLong-L1-32B", "object": "model", "created": 1626777600, "owned_by": "custom", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "Tongyi-Zhiwen/QwenLong-L1-32B", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/qwen-color.svg", "description": "阿里的擅长数学推理、逻辑推理和多跳推理等复杂任务开源模型", "capabilities": {"vision": true, "file_upload": true, "web_search": true, "image_generation": true, "code_interpreter": true, "citations": true}, "suggestion_prompts": null, "tags": [{"name": "qwen"}, {"name": "<PERSON>"}]}, "access_control": {"read": {"group_ids": [], "user_ids": []}, "write": {"group_ids": [], "user_ids": []}}, "price": {"prompt_price": 0, "completion_price": 0, "request_price": 0, "minimum_credit": 0}, "is_active": true, "updated_at": 1748662052, "created_at": 1748662052}, {"id": "tts-1", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1", "parent": null, "connection_type": "external", "name": "tts-1", "openai": {"id": "tts-1", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "TTS"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615400, "created_at": 1748615400}, {"id": "tts-1-1106", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1-1106", "parent": null, "connection_type": "external", "name": "tts-1-1106", "openai": {"id": "tts-1-1106", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1-1106", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "TTS"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615401, "created_at": 1748615401}, {"id": "tts-1-hd", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1-hd", "parent": null, "connection_type": "external", "name": "tts-1-hd", "openai": {"id": "tts-1-hd", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1-hd", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "TTS"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615402, "created_at": 1748615402}, {"id": "tts-1-hd-1106", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1-hd-1106", "parent": null, "connection_type": "external", "name": "tts-1-hd-1106", "openai": {"id": "tts-1-hd-1106", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "tts-1-hd-1106", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/dalle-color.svg", "description": null, "capabilities": null, "tags": [{"name": "TTS"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615403, "created_at": 1748615403}, {"id": "whisper-1", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "whisper-1", "parent": null, "connection_type": "external", "name": "whisper-1", "openai": {"id": "whisper-1", "object": "model", "created": 1626777600, "owned_by": "openai", "permission": [{"id": "modelperm-LwHkVFn8AcMItP432fKKDIKJ", "object": "model_permission", "created": 1626777600, "allow_create_engine": true, "allow_sampling": true, "allow_logprobs": true, "allow_search_indices": false, "allow_view": true, "allow_fine_tuning": false, "organization": "*", "group": null, "is_blocking": false}], "root": "whisper-1", "parent": null, "connection_type": "external"}, "urlIdx": 0, "user_id": "9efd4485-6170-4c3f-9d93-a4323d179714", "base_model_id": null, "params": {}, "meta": {"profile_image_url": "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "description": null, "capabilities": null, "tags": [{"name": "Whisper"}]}, "access_control": {}, "price": null, "is_active": false, "updated_at": 1748615403, "created_at": 1748615403}]