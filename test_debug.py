#!/usr/bin/env python3
# 测试脚本，用于调试图片URL无法修改的问题

import json

# 从main.py导入相关函数和变量
from main import find_correct_url, PROVIDER_MAP, DEFAULT_URLS

def test_url_matching():
    """测试URL匹配功能"""
    print("=== 测试URL匹配功能 ===")
    
    # 测试一些常见的模型ID
    test_model_ids = [
        "gpt-4",
        "gpt-3.5-turbo", 
        "claude-3-sonnet",
        "gemini-pro",
        "qwen-max",
        "deepseek-chat",
        "openrouter/anthropic/claude-3-sonnet",
        "free/gpt-4",
        "unknown-model"
    ]
    
    print("PROVIDER_MAP 前几项:")
    for i, (key, url) in enumerate(PROVIDER_MAP[:5]):
        print(f"  {i+1}. '{key}' -> '{url}'")
    print(f"  ... (共 {len(PROVIDER_MAP)} 项)")
    print()
    
    print("DEFAULT_URLS:", DEFAULT_URLS)
    print()
    
    for model_id in test_model_ids:
        result = find_correct_url(model_id)
        print(f"模型ID: '{model_id}' -> URL: {result}")
    print()

def test_url_update_logic():
    """测试URL更新逻辑"""
    print("=== 测试URL更新逻辑 ===")
    
    # 模拟不同的current_url情况
    test_cases = [
        ("gpt-4", "", "空URL"),
        ("gpt-4", "static/favicon.png", "默认URL"),
        ("gpt-4", "https://registry.npmmirror.com/@lobehub/icons-static-svg/latest/files/icons/openai.svg", "正确URL"),
        ("gpt-4", "https://example.com/custom.png", "用户自定义URL"),
        ("unknown-model", "", "未知模型")
    ]
    
    for model_id, current_url, description in test_cases:
        correct_url = find_correct_url(model_id)
        
        print(f"\n测试案例: {description}")
        print(f"  模型ID: {model_id}")
        print(f"  当前URL: '{current_url}'")
        print(f"  匹配到的URL: {correct_url}")
        
        # 模拟更新逻辑
        if correct_url:
            if current_url == correct_url:
                action = "跳过 (已是正确URL)"
            elif current_url in DEFAULT_URLS or not current_url:
                action = "更新 (空或默认URL)"
            else:
                action = "跳过 (保留用户自定义)"
        else:
            action = "跳过 (未匹配到提供商)"
        
        print(f"  处理动作: {action}")

if __name__ == "__main__":
    test_url_matching()
    test_url_update_logic()
